#!/usr/bin/env python3
"""
MG996R 360度舵机调试和校准工具
专门解决舵机只能单向转动的问题

使用方法：
1. 运行此脚本进行舵机测试
2. 根据测试结果调整PWM参数
3. 将有效参数应用到main.py中

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time
import sys

class ServoDebugger:
    def __init__(self):
        """初始化舵机调试器"""
        try:
            # 配置引脚
            pinmap.set_pin_function("A18", "PWM6")  # 水平舵机
            
            # 初始化PWM
            self.horizontal_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
            print("✓ 水平舵机PWM6初始化成功")
            
            # 当前测试的PWM参数
            self.current_stop = 7.5
            self.current_cw = 8.5
            self.current_ccw = 6.5
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            self.horizontal_pwm = None

    def test_pwm_range(self):
        """测试不同PWM占空比范围"""
        if self.horizontal_pwm is None:
            print("❌ PWM未初始化")
            return
            
        print("\n🔧 PWM范围测试")
        print("="*30)
        
        # 测试范围：5.0% 到 10.0%，步长0.5%
        test_range = [i * 0.5 + 5.0 for i in range(11)]  # 5.0, 5.5, 6.0, ..., 10.0
        
        results = {}
        
        for duty in test_range:
            print(f"\n测试PWM占空比: {duty:.1f}%")
            try:
                self.horizontal_pwm.duty(duty)
                time.sleep(2)
                
                if duty < 7.5:
                    expected = "逆时针"
                elif duty > 7.5:
                    expected = "顺时针"
                else:
                    expected = "停止"
                    
                print(f"  预期: {expected}")
                print(f"  实际观察到的转动情况: (请手动记录)")
                
                results[duty] = expected
                
            except Exception as e:
                print(f"  ❌ 设置失败: {e}")
                
        # 回到停止位置
        self.horizontal_pwm.duty(7.5)
        
        print("\n测试完成！请根据观察结果调整参数")
        return results

    def test_bidirectional(self):
        """测试双向转动能力 - 每次转动后回到停止位置"""
        if self.horizontal_pwm is None:
            print("❌ PWM未初始化")
            return

        print("\n🔄 双向转动测试")
        print("="*25)
        print("测试模式：转动 → 停止 → 转动 → 停止")

        # 改进的测试序列 - 每次转动后都回到停止位置
        test_sequence = [
            (7.5, "初始停止位置", 2),

            # 小幅度测试
            (8.0, "小幅顺时针（向右）", 3),
            (7.5, "回到停止位置", 2),
            (7.0, "小幅逆时针（向左）", 3),
            (7.5, "回到停止位置", 2),

            # 中等幅度测试
            (8.5, "中等顺时针（向右）", 3),
            (7.5, "回到停止位置", 2),
            (6.5, "中等逆时针（向左）", 3),
            (7.5, "回到停止位置", 2),

            # 较大幅度测试
            (9.0, "较快顺时针（向右）", 3),
            (7.5, "回到停止位置", 2),
            (6.0, "较快逆时针（向左）", 3),
            (7.5, "最终停止位置", 2),
        ]

        for duty, description, duration in test_sequence:
            print(f"\n设置: {duty:.1f}% ({description})")
            try:
                self.horizontal_pwm.duty(duty)

                # 显示预期行为
                if duty > 7.5:
                    print(f"  → 预期：顺时针转动（向右）")
                elif duty < 7.5:
                    print(f"  ← 预期：逆时针转动（向左）")
                else:
                    print(f"  ⏸ 预期：完全停止")

                time.sleep(duration)
                print(f"  ✓ 执行{duration}秒完成")
            except Exception as e:
                print(f"  ❌ 失败: {e}")

        print("\n双向测试完成！")
        print("请根据观察结果判断舵机是否能正常双向转动")

    def find_working_range(self):
        """寻找有效的工作范围"""
        if self.horizontal_pwm is None:
            print("❌ PWM未初始化")
            return
            
        print("\n🎯 寻找有效工作范围")
        print("="*30)
        
        # 从停止位置开始，逐步增加/减少PWM值
        stop_duty = 7.5
        
        print("测试顺时针方向...")
        cw_working = []
        for offset in [0.1, 0.2, 0.3, 0.4, 0.5, 0.7, 1.0, 1.5, 2.0]:
            duty = stop_duty + offset
            if duty > 10.0:  # 安全限制
                break
                
            print(f"  测试 {duty:.1f}%")
            try:
                self.horizontal_pwm.duty(duty)
                time.sleep(2)
                print(f"    PWM设置成功，观察是否顺时针转动")
                # 这里需要人工观察
                cw_working.append(duty)
            except Exception as e:
                print(f"    ❌ 失败: {e}")
                
        # 回到停止位置
        self.horizontal_pwm.duty(stop_duty)
        time.sleep(1)
        
        print("测试逆时针方向...")
        ccw_working = []
        for offset in [0.1, 0.2, 0.3, 0.4, 0.5, 0.7, 1.0, 1.5, 2.0]:
            duty = stop_duty - offset
            if duty < 5.0:  # 安全限制
                break
                
            print(f"  测试 {duty:.1f}%")
            try:
                self.horizontal_pwm.duty(duty)
                time.sleep(2)
                print(f"    PWM设置成功，观察是否逆时针转动")
                # 这里需要人工观察
                ccw_working.append(duty)
            except Exception as e:
                print(f"    ❌ 失败: {e}")
                
        # 回到停止位置
        self.horizontal_pwm.duty(stop_duty)
        
        print(f"\n测试完成！")
        print(f"顺时针测试范围: {cw_working}")
        print(f"逆时针测试范围: {ccw_working}")

    def manual_control(self):
        """手动控制模式 - 预设测试序列（适用于MaixCAM）"""
        if self.horizontal_pwm is None:
            print("❌ PWM未初始化")
            return

        print("\n🎮 手动控制模式 - 预设测试序列")
        print("="*35)
        print("由于MaixCAM环境限制，使用预设测试序列")

        # 预设的测试序列，涵盖常用PWM值
        test_sequence = [
            (6.0, "强逆时针"),
            (6.5, "中逆时针"),
            (7.0, "弱逆时针"),
            (7.5, "停止位置"),
            (8.0, "弱顺时针"),
            (8.5, "中顺时针"),
            (9.0, "强顺时针"),
            (7.5, "回到停止")
        ]

        try:
            for duty, description in test_sequence:
                print(f"\n设置PWM: {duty:.1f}% ({description})")
                self.horizontal_pwm.duty(duty)
                time.sleep(3)  # 观察3秒
                print(f"  ✓ 已设置，请观察舵机转动情况")

        except KeyboardInterrupt:
            print("\n用户中断测试")
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")

        # 确保回到停止位置
        try:
            self.horizontal_pwm.duty(7.5)
            print("\n✓ 已回到停止位置")
        except Exception as e:
            print(f"❌ 回到停止位置失败: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            if self.horizontal_pwm:
                self.horizontal_pwm.duty(7.5)  # 停止舵机
                # MaixPy的PWM对象没有close()方法，只需要停止即可
            print("✓ 舵机已停止，资源清理完成")
        except Exception as e:
            print(f"清理资源时出错: {e}")

def main():
    """主函数"""
    print("🔧 MG996R 360度舵机调试工具")
    print("="*40)
    
    debugger = ServoDebugger()
    
    if debugger.horizontal_pwm is None:
        print("初始化失败，程序退出")
        return
        
    try:
        print("\n🔧 自动执行舵机诊断程序")
        print("="*30)
        print("将依次执行以下测试：")
        print("1. 双向转动测试")
        print("2. PWM范围测试")
        print("3. 寻找有效工作范围")

        # 自动执行所有测试，适合MaixCAM环境
        print("\n开始双向转动测试...")
        debugger.test_bidirectional()

        print("\n等待3秒后继续...")
        time.sleep(3)

        print("\n开始PWM范围测试...")
        debugger.test_pwm_range()

        print("\n等待3秒后继续...")
        time.sleep(3)

        print("\n开始寻找有效工作范围...")
        debugger.find_working_range()

        print("\n所有测试完成！")
        print("请根据观察到的舵机行为调整main.py中的PWM参数")
            
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")
    finally:
        debugger.cleanup()

if __name__ == "__main__":
    main()
