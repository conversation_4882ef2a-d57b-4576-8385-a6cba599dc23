#!/usr/bin/env python3
"""
MG996R 360度舵机快速测试脚本
专门解决只能单向转动的问题

@author: AI Assistant  
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def test_servo():
    """快速测试舵机双向转动"""
    print("🔧 MG996R 360度舵机快速测试")
    print("="*35)
    
    try:
        # 初始化PWM
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        # 测试序列 - 专门针对双向转动问题
        test_sequence = [
            (7.5, "停止位置", 2),
            (8.0, "小幅顺时针", 3),
            (7.5, "停止", 1),
            (7.0, "小幅逆时针", 3),
            (7.5, "停止", 1),
            (8.5, "中等顺时针", 3),
            (7.5, "停止", 1),
            (6.5, "中等逆时针", 3),
            (7.5, "停止", 1),
            (9.0, "较快顺时针", 3),
            (7.5, "停止", 1),
            (6.0, "较快逆时针", 3),
            (7.5, "最终停止", 2)
        ]
        
        print("\n开始测试序列...")
        print("请仔细观察舵机转动方向：")
        print("- 顺时针：应该向右转")
        print("- 逆时针：应该向左转")
        print("- 停止：应该完全停止")
        
        for duty, description, duration in test_sequence:
            print(f"\n设置: {duty:.1f}% ({description})")
            servo_pwm.duty(duty)
            
            # 显示预期行为
            if duty > 7.5:
                expected = "→ 预期：顺时针转动"
            elif duty < 7.5:
                expected = "← 预期：逆时针转动"
            else:
                expected = "⏸ 预期：停止"
            
            print(f"  {expected}")
            time.sleep(duration)
        
        print("\n" + "="*35)
        print("测试完成！请根据观察结果判断：")
        print("\n✅ 如果舵机能双向转动：")
        print("   - 问题已解决！")
        print("   - 可以正常使用main.py")
        
        print("\n❌ 如果舵机只能单向转动：")
        print("   - 可能需要调整PWM范围")
        print("   - 尝试以下参数：")
        print("     方案A: 顺时针8.0%, 逆时针7.0%")
        print("     方案B: 顺时针9.0%, 逆时针6.0%")
        print("     方案C: 检查舵机是否为真正的360度版本")
        
        print("\n⚠️ 如果舵机完全不动：")
        print("   - 检查电源供应（需要足够电流）")
        print("   - 检查信号线连接（A18引脚）")
        print("   - 检查舵机本身是否损坏")
        
        print("\n🔄 如果转动方向相反：")
        print("   - 修改main.py第254行")
        print("   - 将 horizontal_error = -err_x")
        print("   - 改为 horizontal_error = err_x")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("请检查：")
        print("1. MaixCAM Pro是否支持PWM6")
        print("2. A18引脚连接是否正确")
        print("3. 舵机电源是否正常")
    
    print("\n程序结束")

if __name__ == "__main__":
    test_servo()
