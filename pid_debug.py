#!/usr/bin/env python3
"""
PID控制调试脚本
专门调试为什么水平舵机在PID控制下不动

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

class PIDDebugger:
    def __init__(self):
        """初始化PID调试器"""
        try:
            # 配置引脚
            pinmap.set_pin_function("A18", "PWM6")  # 水平舵机
            
            # 初始化PWM
            self.horizontal_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
            print("✓ 水平舵机PWM6初始化成功")
            
            # 水平舵机参数（与main.py保持一致）
            self.horizontal_stop_duty = 7.5
            self.horizontal_cw_duty = 8.5
            self.horizontal_ccw_duty = 6.5
            
            # PID参数（与main.py保持一致）
            self.horizontal_pid_kp = 0.06
            self.horizontal_pid_ki = 0.001
            self.horizontal_pid_kd = 0.008
            
            # PID状态变量
            self.horizontal_error_sum = 0
            self.horizontal_last_error = 0
            
            # 速度控制参数
            self.global_speed_multiplier = 0.6
            self.speed_scale_factor = 1.0
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            self.horizontal_pwm = None

    def set_horizontal_speed(self, speed):
        """设置水平舵机速度（与main.py完全一致）"""
        if self.horizontal_pwm is None:
            print("警告：水平舵机PWM未初始化")
            return

        original_speed = speed
        speed = max(-100, min(100, speed))
        speed = speed * self.speed_scale_factor * self.global_speed_multiplier

        print(f"🔧 速度处理: {original_speed:.1f} → {speed:.1f} (缩放: {self.speed_scale_factor * self.global_speed_multiplier:.3f})")

        # 速度死区
        if abs(speed) < 0.1:
            speed = 0
            print(f"  → 速度太小，设为0")

        if speed == 0:
            duty = self.horizontal_stop_duty
        elif speed > 0:
            duty_range = self.horizontal_cw_duty - self.horizontal_stop_duty
            duty_change = (abs(speed) / 100.0) * duty_range
            duty = self.horizontal_stop_duty + duty_change
            duty = min(duty, self.horizontal_cw_duty)
        else:
            duty_range = self.horizontal_stop_duty - self.horizontal_ccw_duty
            duty_change = (abs(speed) / 100.0) * duty_range
            duty = self.horizontal_stop_duty - duty_change
            duty = max(duty, self.horizontal_ccw_duty)

        try:
            self.horizontal_pwm.duty(duty)
            print(f"✓ PWM设置: 速度={speed:.1f}%, 占空比={duty:.3f}%")
        except Exception as e:
            print(f"❌ PWM设置失败: {e}")

    def test_pid_logic(self, err_x):
        """测试PID逻辑（与main.py完全一致）"""
        print(f"\n🔍 测试PID逻辑: err_x = {err_x}")
        
        horizontal_error = -err_x  # 与main.py保持一致
        print(f"  horizontal_error = {horizontal_error}")
        
        # 逼近阈值（与main.py保持一致）
        approach_threshold = 3
        fine_zone = 12
        slow_zone = 25
        normal_zone = 50
        fine_max_speed = 4
        slow_max_speed = 8
        normal_max_speed = 12
        pid_max_speed = 16
        
        if abs(horizontal_error) <= approach_threshold:
            horizontal_speed = 0
            self.horizontal_error_sum = 0
            print(f"  → 逼近区域: 停止 (误差={horizontal_error:.1f} <= {approach_threshold})")
        elif abs(horizontal_error) <= fine_zone:
            horizontal_speed = horizontal_error * 0.3
            horizontal_speed = max(-fine_max_speed, min(fine_max_speed, horizontal_speed))
            print(f"  → 精细区域: 速度={horizontal_speed:.1f}% (误差={horizontal_error:.1f})")
        elif abs(horizontal_error) <= slow_zone:
            horizontal_speed = horizontal_error * 0.4
            horizontal_speed = max(-slow_max_speed, min(slow_max_speed, horizontal_speed))
            print(f"  → 减速区域: 速度={horizontal_speed:.1f}% (误差={horizontal_error:.1f})")
        elif abs(horizontal_error) <= normal_zone:
            horizontal_speed = horizontal_error * 0.5
            horizontal_speed = max(-normal_max_speed, min(normal_max_speed, horizontal_speed))
            print(f"  → 正常区域: 速度={horizontal_speed:.1f}% (误差={horizontal_error:.1f})")
        else:
            # PID控制
            self.horizontal_error_sum += horizontal_error
            self.horizontal_error_sum = max(-50, min(50, self.horizontal_error_sum))
            
            horizontal_derivative = horizontal_error - self.horizontal_last_error
            
            horizontal_output = (self.horizontal_pid_kp * horizontal_error +
                               self.horizontal_pid_ki * self.horizontal_error_sum +
                               self.horizontal_pid_kd * horizontal_derivative)
            
            horizontal_speed = max(-pid_max_speed, min(pid_max_speed, horizontal_output))
            print(f"  → PID区域: 输出={horizontal_output:.2f}, 速度={horizontal_speed:.1f}%")
            print(f"    P={self.horizontal_pid_kp * horizontal_error:.2f}, I={self.horizontal_pid_ki * self.horizontal_error_sum:.2f}, D={self.horizontal_pid_kd * horizontal_derivative:.2f}")

        # 最终速度过滤
        if abs(horizontal_speed) < 0.1:
            print(f"  → 速度过滤: {horizontal_speed:.1f}% → 0% (太小)")
            horizontal_speed = 0

        print(f"  → 最终速度: {horizontal_speed:.1f}%")
        
        # 设置舵机速度
        self.set_horizontal_speed(horizontal_speed)
        
        # 更新上次误差
        self.horizontal_last_error = horizontal_error
        
        return horizontal_speed

    def comprehensive_test(self):
        """全面测试PID控制"""
        print("\n🔧 全面PID控制测试")
        print("="*30)
        
        # 测试不同误差值
        test_errors = [
            100,   # 大误差，应该触发PID控制
            50,    # 中等误差，应该触发正常区域控制
            20,    # 小误差，应该触发减速区域控制
            10,    # 很小误差，应该触发精细区域控制
            2,     # 极小误差，应该停止
            -2,    # 极小负误差，应该停止
            -10,   # 小负误差
            -20,   # 中等负误差
            -50,   # 大负误差
            0      # 无误差
        ]
        
        for err_x in test_errors:
            speed = self.test_pid_logic(err_x)
            time.sleep(2)  # 观察舵机行为
            
            # 停止舵机
            self.set_horizontal_speed(0)
            time.sleep(1)
        
        print("\n全面测试完成！")

    def step_by_step_debug(self):
        """逐步调试PID问题"""
        print("\n🔍 逐步调试PID问题")
        print("="*25)
        
        # 步骤1：测试基本PWM控制
        print("\n步骤1: 测试基本PWM控制")
        self.set_horizontal_speed(10)
        time.sleep(2)
        self.set_horizontal_speed(0)
        time.sleep(1)
        
        # 步骤2：测试不同的误差值
        print("\n步骤2: 测试中等误差 (err_x=30)")
        self.test_pid_logic(30)
        time.sleep(3)
        self.set_horizontal_speed(0)
        time.sleep(1)
        
        print("\n步骤3: 测试小误差 (err_x=10)")
        self.test_pid_logic(10)
        time.sleep(3)
        self.set_horizontal_speed(0)
        time.sleep(1)
        
        print("\n步骤4: 测试负误差 (err_x=-20)")
        self.test_pid_logic(-20)
        time.sleep(3)
        self.set_horizontal_speed(0)
        time.sleep(1)
        
        print("\n逐步调试完成！")

    def cleanup(self):
        """清理资源"""
        try:
            if self.horizontal_pwm:
                self.horizontal_pwm.duty(7.5)
            print("✓ 舵机已停止")
        except Exception as e:
            print(f"清理资源时出错: {e}")

def main():
    """主函数"""
    print("🔧 PID控制调试工具")
    print("="*25)
    
    debugger = PIDDebugger()
    
    if debugger.horizontal_pwm is None:
        print("初始化失败，程序退出")
        return
        
    try:
        print("\n选择测试模式：")
        print("1. 全面测试")
        print("2. 逐步调试")
        
        # 默认执行逐步调试
        choice = "2"
        
        if choice == "1":
            debugger.comprehensive_test()
        else:
            debugger.step_by_step_debug()
            
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")
    finally:
        debugger.cleanup()

if __name__ == "__main__":
    main()
