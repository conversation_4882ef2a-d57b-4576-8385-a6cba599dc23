#!/usr/bin/env python3
"""
验证新的PWM参数
测试基于实测PWM范围的舵机控制

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def test_new_parameters():
    """测试新的PWM参数"""
    print("🧪 验证新PWM参数")
    print("="*20)
    
    try:
        # 初始化PWM
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.65, enable=True)
        print("✓ PWM初始化成功")
        
        # 新参数
        horizontal_stop_duty = 7.65
        horizontal_cw_duty = 9.0
        horizontal_ccw_duty = 6.0
        global_speed_multiplier = 1.5
        
        print(f"新参数: 停止={horizontal_stop_duty}, 顺时针={horizontal_cw_duty}, 逆时针={horizontal_ccw_duty}")
        print(f"全局倍数: {global_speed_multiplier}")
        
        def test_speed(speed, description):
            """测试指定速度"""
            print(f"\n{description}: {speed}%")
            
            # 应用缩放
            final_speed = speed * global_speed_multiplier
            
            # PWM计算（与main.py一致）
            if final_speed == 0:
                duty = horizontal_stop_duty
            elif final_speed > 0:
                duty_range = horizontal_cw_duty - horizontal_stop_duty
                duty_change = (abs(final_speed) / 100.0) * duty_range
                duty = horizontal_stop_duty + duty_change
                duty = min(duty, horizontal_cw_duty)
            else:
                duty_range = horizontal_stop_duty - horizontal_ccw_duty
                duty_change = (abs(final_speed) / 100.0) * duty_range
                duty = horizontal_stop_duty - duty_change
                duty = max(duty, horizontal_ccw_duty)
            
            change = duty - horizontal_stop_duty
            
            print(f"  最终速度: {final_speed:.1f}%")
            print(f"  PWM占空比: {duty:.3f}% (变化: {change:+.3f}%)")
            
            # 设置PWM
            servo_pwm.duty(duty)
            time.sleep(3)
            
            return duty
        
        # 测试不同速度
        test_cases = [
            (0, "停止测试"),
            (5, "小速度顺时针"),
            (-5, "小速度逆时针"),
            (10, "中速度顺时针"),
            (-10, "中速度逆时针"),
            (20, "高速度顺时针"),
            (-20, "高速度逆时针"),
            (0, "最终停止")
        ]
        
        print("\n开始速度测试...")
        
        for speed, description in test_cases:
            duty = test_speed(speed, description)
            
            # 回到停止位置
            if speed != 0:
                servo_pwm.duty(horizontal_stop_duty)
                time.sleep(1)
        
        print("\n" + "="*20)
        print("新参数验证完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_pid_simulation():
    """模拟PID控制测试"""
    print("\n🧮 模拟PID控制测试")
    print("="*25)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.65, enable=True)
        print("✓ PWM初始化成功")
        
        # 参数设置
        horizontal_stop_duty = 7.65
        horizontal_cw_duty = 9.0
        horizontal_ccw_duty = 6.0
        global_speed_multiplier = 1.5
        
        def simulate_pid_control(err_x, description):
            """模拟PID控制逻辑"""
            print(f"\n{description}: err_x={err_x}px")
            
            # PID逻辑（与main.py一致）
            horizontal_error = -err_x
            
            # 控制区域判断
            if abs(horizontal_error) <= 3:
                horizontal_speed = 0
                zone = "逼近区域"
            elif abs(horizontal_error) <= 12:
                horizontal_speed = horizontal_error * 1.2
                horizontal_speed = max(-6, min(6, horizontal_speed))
                zone = "精细区域"
            elif abs(horizontal_error) <= 25:
                horizontal_speed = horizontal_error * 0.8
                horizontal_speed = max(-10, min(10, horizontal_speed))
                zone = "减速区域"
            elif abs(horizontal_error) <= 50:
                horizontal_speed = horizontal_error * 1.0
                horizontal_speed = max(-15, min(15, horizontal_speed))
                zone = "正常区域"
            else:
                horizontal_speed = horizontal_error * 1.2
                horizontal_speed = max(-20, min(20, horizontal_speed))
                zone = "PID区域"
            
            print(f"  {zone}: horizontal_error={horizontal_error}, speed={horizontal_speed:.1f}%")
            
            # 应用缩放和PWM计算
            final_speed = horizontal_speed * global_speed_multiplier
            
            if final_speed == 0:
                duty = horizontal_stop_duty
            elif final_speed > 0:
                duty_range = horizontal_cw_duty - horizontal_stop_duty
                duty_change = (abs(final_speed) / 100.0) * duty_range
                duty = horizontal_stop_duty + duty_change
                duty = min(duty, horizontal_cw_duty)
            else:
                duty_range = horizontal_stop_duty - horizontal_ccw_duty
                duty_change = (abs(final_speed) / 100.0) * duty_range
                duty = horizontal_stop_duty - duty_change
                duty = max(duty, horizontal_ccw_duty)
            
            change = duty - horizontal_stop_duty
            
            print(f"  最终速度: {final_speed:.1f}%")
            print(f"  PWM占空比: {duty:.3f}% (变化: {change:+.3f}%)")
            
            # 设置PWM
            servo_pwm.duty(duty)
            time.sleep(3)
            
            # 回到停止
            servo_pwm.duty(horizontal_stop_duty)
            time.sleep(1)
        
        # 测试不同误差值
        pid_test_cases = [
            (5, "小误差向右"),
            (-5, "小误差向左"),
            (15, "中等误差向右"),
            (-15, "中等误差向左"),
            (30, "大误差向右"),
            (-30, "大误差向左")
        ]
        
        print("模拟PID控制测试...")
        
        for err_x, description in pid_test_cases:
            simulate_pid_control(err_x, description)
        
        print("PID模拟测试完成！")
        
    except Exception as e:
        print(f"❌ PID测试失败: {e}")

def main():
    """主函数"""
    print("🧪 验证新PWM参数")
    print("="*25)
    
    try:
        print("基于实测结果的新参数：")
        print("- 停止位置: 7.65%")
        print("- 顺时针开始: 7.66% (范围到9.0%)")
        print("- 逆时针开始: 7.15% (范围到6.0%)")
        
        # 验证新参数
        test_new_parameters()
        
        # 模拟PID控制
        test_pid_simulation()
        
        print("\n" + "="*25)
        print("🏁 验证完成！")
        print("\n📋 如果测试成功：")
        print("✅ 舵机应该能双向转动")
        print("✅ PID控制应该正常工作")
        print("✅ 可以正常运行main.py")
        
        print("\n🚀 下一步：")
        print("1. 运行 python main.py")
        print("2. 测试目标跟踪功能")
        print("3. 如果还有问题，可能需要微调PID参数")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
