'''
    2025电赛E题找A4 UV纸圆心，可以找到圆心和第三个圆圈，帧率 > 25fps。
    有多种设置和算法，根据实际情况选择。
    控制云台可以基于中心点误差 err_center 进行 PID 控制
    <AUTHOR> & lxo@sieed 协助
    @license MIT
    @date 2025.7.30
'''

from maix import camera, display, image, nn, app, time, pwm, pinmap
disp = display.Display()
img = image.Image(disp.width(), disp.height())
msg = "Loading ..."
size = image.string_size(msg, scale = 1.5, thickness=2)
img.draw_string((img.width() - size.width()) // 2, (img.height() - size.height()) // 2, msg, scale=1.5, thickness=2)
disp.show(img)

import cv2
import numpy as np
import os
import math

# 舵机速度配置参数 - 直接内嵌，避免导入问题
print("使用内嵌舵机速度配置")

# ==================== 舵机速度控制参数 ====================

# 全局速度倍数 (0.1-1.0)
# 0.1 = 非常慢, 0.3 = 慢, 0.6 = 中等, 0.8 = 快, 1.0 = 最快
GLOBAL_SPEED_MULTIPLIER = 1.0  # 增大到1.0，确保舵机有足够的驱动力

# 水平舵机基础速度参数
HORIZONTAL_MAX_SPEED = 12      # 最大速度百分比 (5-30) - 降低最大速度
HORIZONTAL_MIN_SPEED = 2       # 最小有效速度百分比 (1-10) - 降低最小速度

# 垂直舵机速度参数
VERTICAL_MAX_STEP = 2.0        # 垂直舵机每次最大角度变化 (0.5-5.0度)

# 垂直舵机PID参数
VERTICAL_PID_KP = 0.08         # 比例系数 (0.05-0.2)
VERTICAL_PID_KI = 0.005        # 积分系数 (0.001-0.02)
VERTICAL_PID_KD = 0.02         # 微分系数 (0.01-0.1)

# 水平舵机PID参数
HORIZONTAL_PID_KP = 0.06       # 比例系数 (0.03-0.15)
HORIZONTAL_PID_KI = 0.001      # 积分系数 (0.001-0.01)
HORIZONTAL_PID_KD = 0.008      # 微分系数 (0.005-0.05)

# 逼近控制阈值
APPROACH_THRESHOLD = 3         # 停止阈值 (3-10像素) - 减小阈值，让舵机更容易启动
FINE_ZONE = 12                 # 精细控制区 (8-20像素)
SLOW_ZONE = 25                 # 减速区 (15-40像素)
NORMAL_ZONE = 50               # 正常控制区 (30-80像素)

# 速度限制 (在不同区域的最大速度) - 降低所有区域的速度
FINE_ZONE_MAX_SPEED = 4        # 精细区最大速度 (3-10) - 更慢更精确
SLOW_ZONE_MAX_SPEED = 8        # 减速区最大速度 (8-15) - 降低速度
NORMAL_ZONE_MAX_SPEED = 12     # 正常区最大速度 (12-25) - 降低速度
PID_ZONE_MAX_SPEED = 16        # PID区最大速度 (15-30) - 降低速度

print(f"舵机配置已加载 - 当前为慢速安全配置")
print(f"全局速度倍数: {GLOBAL_SPEED_MULTIPLIER}")
print("=" * 50)
print("🔧 舵机速度调节说明:")
print(f"   如果舵机移动太快 → 减小 GLOBAL_SPEED_MULTIPLIER (当前: {GLOBAL_SPEED_MULTIPLIER})")
print(f"   如果舵机移动太慢 → 增大 GLOBAL_SPEED_MULTIPLIER (最大: 1.0)")
print("   推荐值: 0.2(很慢) 0.3(慢) 0.5(中等) 0.7(快)")
print("   修改位置: main.py 第30行")
print("=" * 50)

# 舵机控制类
class ServoController:
    def __init__(self):
        """初始化舵机控制器"""
        try:
            # 参考E题.py的正确初始化方式
            # 配置引脚功能为PWM (根据MaixCam Pro引脚定义)
            pinmap.set_pin_function("A19", "PWM7")  # 垂直舵机 (上下转动) - PWM7通道
            pinmap.set_pin_function("A18", "PWM6")  # 水平舵机 (左右转动) - PWM6通道

            # 初始化PWM (MG996R舵机标准频率50Hz)
            # 根据官方文档，PWM构造函数参数为 (pwm_id, freq, duty, enable)
            print("正在初始化PWM...")
            self.vertical_pwm = pwm.PWM(7, freq=50, duty=7.5, enable=True)    # PWM通道7，初始中位(7.5%占空比)
            print("✓ 垂直舵机PWM7初始化成功")

            self.horizontal_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)  # PWM通道6，初始中位(7.5%占空比)
            print("✓ 水平舵机PWM6初始化成功")

            print("舵机PWM初始化完成")
            print("A19引脚 -> PWM7 -> 垂直舵机 (180°位置控制)")
            print("A18引脚 -> PWM6 -> 水平舵机 (360°速度控制)")

            # 验证PWM对象是否有效
            if self.horizontal_pwm is None:
                raise Exception("水平舵机PWM初始化失败")
            if self.vertical_pwm is None:
                raise Exception("垂直舵机PWM初始化失败")

            # 垂直舵机参数（180度位置舵机）
            self.vertical_min_duty = 2.5    # 0度对应的占空比
            self.vertical_max_duty = 12.5   # 180度对应的占空比
            self.vertical_center_duty = 7.5 # 90度中位占空比
            self.vertical_current_angle = 90 # 当前角度

            # 水平舵机参数（360度连续旋转舵机）- MG996R增强参数
            # 增大PWM范围，确保舵机有足够的驱动力
            self.horizontal_stop_duty = 7.5    # 停止 (1.5ms) - 标准停止位置
            self.horizontal_cw_duty = 9.0      # 顺时针 (1.8ms) - 增大范围，确保能转动
            self.horizontal_ccw_duty = 6.0     # 逆时针 (1.2ms) - 增大范围，确保能转动
            self.horizontal_deadzone = 0.1     # 死区范围 ±0.1%

            # 速度控制参数 - 使用配置文件中的参数
            self.max_speed_percent = HORIZONTAL_MAX_SPEED  # 从配置文件读取
            self.min_speed_percent = 3         # 最小有效速度百分比
            self.speed_scale_factor = 2.0      # 基础速度缩放因子（增大到2.0，确保足够的驱动力）

            # 可调节的速度参数
            self.global_speed_multiplier = GLOBAL_SPEED_MULTIPLIER  # 从配置文件读取

            # 垂直舵机PID控制参数
            self.vertical_pid_kp = 0.1      # 垂直比例系数
            self.vertical_pid_ki = 0.01     # 垂直积分系数
            self.vertical_pid_kd = 0.05     # 垂直微分系数

            # 水平舵机PID控制参数
            self.horizontal_pid_kp = 0.5    # 水平比例系数（更大）
            self.horizontal_pid_ki = 0.02   # 水平积分系数
            self.horizontal_pid_kd = 0.1    # 水平微分系数

            # PID状态变量
            self.vertical_error_sum = 0
            self.horizontal_error_sum = 0
            self.vertical_last_error = 0
            self.horizontal_last_error = 0

            print("舵机控制器初始化成功")
            print(f"垂直舵机PWM7初始化完成，当前占空比: 7.5%")
            print(f"水平舵机PWM6初始化完成，当前占空比: 7.5%")

        except Exception as e:
            print(f"舵机控制器初始化失败: {e}")
            print("请检查：")
            print("1. MaixCAM Pro是否支持PWM6和PWM7")
            print("2. A18和A19引脚是否正确连接")
            print("3. 舵机电源是否正常供电")
            self.vertical_pwm = None
            self.horizontal_pwm = None

    def vertical_angle_to_duty(self, angle):
        """将角度转换为PWM占空比 (垂直180度舵机)"""
        # 限制角度范围在 0 到 180 度之间
        angle = max(0, min(180, angle))

        # 线性映射：0度 -> 2.5%, 90度 -> 7.5%, 180度 -> 12.5%
        duty = self.vertical_min_duty + (angle / 180.0) * (self.vertical_max_duty - self.vertical_min_duty)
        return duty

    def set_vertical_angle(self, angle):
        """设置垂直舵机角度 (0-180度)"""
        if self.vertical_pwm is None:
            print("警告：垂直舵机PWM未初始化")
            return

        # 限制角度范围
        angle = max(0, min(180, angle))

        duty = self.vertical_angle_to_duty(angle)
        try:
            self.vertical_pwm.duty(duty)
            self.vertical_current_angle = angle
            print(f"垂直角度设置为: {angle}°, 占空比: {duty:.2f}%")
        except Exception as e:
            print(f"设置垂直舵机角度失败: {e}")

    def set_horizontal_speed(self, speed):
        """设置水平舵机速度 (-100到100, 0为停止) - 360度连续旋转舵机"""
        if self.horizontal_pwm is None:
            print("警告：水平舵机PWM未初始化")
            return

        # 限制速度范围并应用多层速度缩放因子
        original_speed = speed
        speed = max(-100, min(100, speed))
        speed = speed * self.speed_scale_factor * self.global_speed_multiplier  # 应用多层速度缩放

        # 调试信息：显示速度缩放过程
        if DEBUG and abs(original_speed) > 0:
            print(f"    速度缩放: {original_speed:.2f} → {speed:.2f} (缩放因子: {self.speed_scale_factor} × {self.global_speed_multiplier} = {self.speed_scale_factor * self.global_speed_multiplier:.3f})")

        # 添加速度死区，小于阈值的速度直接设为0
        speed_deadzone = 0.3  # 适中的死区阈值
        if abs(speed) < speed_deadzone:
            speed = 0

        if speed == 0:
            # 停止 - 使用精确的停止位置
            duty = self.horizontal_stop_duty
        elif speed > 0:
            # 顺时针旋转 - 改进的线性控制
            # 使用更小的PWM范围确保双向都能工作
            duty_range = self.horizontal_cw_duty - self.horizontal_stop_duty  # 1.0%
            duty_change = (abs(speed) / 100.0) * duty_range
            duty = self.horizontal_stop_duty + duty_change
            # 确保不超过最大值
            duty = min(duty, self.horizontal_cw_duty)
        else:
            # 逆时针旋转 - 改进的线性控制
            # 使用更小的PWM范围确保双向都能工作
            duty_range = self.horizontal_stop_duty - self.horizontal_ccw_duty  # 1.0%
            duty_change = (abs(speed) / 100.0) * duty_range
            duty = self.horizontal_stop_duty - duty_change
            # 确保不超过最小值
            duty = max(duty, self.horizontal_ccw_duty)

        try:
            self.horizontal_pwm.duty(duty)
            if DEBUG or abs(speed) > 0:
                print(f"🔧 水平速度设置为: {speed:.1f}%, 占空比: {duty:.3f}% (原始速度: {original_speed:.1f}%)")
        except Exception as e:
            print(f"❌ 设置水平舵机速度失败: {e}")

    def pid_control(self, err_x, err_y):
        """基于误差进行混合PID控制 - 目标逼近控制"""
        if self.vertical_pwm is None or self.horizontal_pwm is None:
            return

        # 垂直方向PID控制 (位置式 - 180度舵机) - 添加速度控制
        vertical_error = err_y  # 注意方向，向上为负
        self.vertical_error_sum += vertical_error
        vertical_derivative = vertical_error - self.vertical_last_error

        vertical_output = (self.vertical_pid_kp * vertical_error +
                          self.vertical_pid_ki * self.vertical_error_sum +
                          self.vertical_pid_kd * vertical_derivative)

        # 限制垂直舵机的移动速度 - 新增速度控制
        try:
            max_vertical_step = VERTICAL_MAX_STEP * self.global_speed_multiplier  # 从配置文件读取
        except NameError:
            max_vertical_step = 2.0 * self.global_speed_multiplier  # 默认值
        vertical_output = max(-max_vertical_step, min(max_vertical_step, vertical_output))

        # 转换为角度调整 (增量式)
        new_vertical_angle = self.vertical_current_angle + vertical_output
        self.set_vertical_angle(new_vertical_angle)

        # 水平方向逼近控制 (360度舵机的目标逼近) - 使用配置文件参数
        # 方向控制逻辑分析：
        # err_x > 0: 目标在画面右侧，摄像头需要向右转
        # err_x < 0: 目标在画面左侧，摄像头需要向左转
        #
        # 舵机控制：
        # speed > 0: 顺时针转（可能是向右转或向左转，取决于安装方向）
        # speed < 0: 逆时针转
        #
        # 如果发现舵机转向错误，请修改下面这行的符号
        horizontal_error = -err_x  # 如果方向错误，改为 -err_x

        # 定义逼近阈值 - 使用配置文件中的参数
        try:
            approach_threshold = APPROACH_THRESHOLD
            fine_zone = FINE_ZONE
            slow_zone = SLOW_ZONE
            normal_zone = NORMAL_ZONE
            fine_max_speed = FINE_ZONE_MAX_SPEED
            slow_max_speed = SLOW_ZONE_MAX_SPEED
            normal_max_speed = NORMAL_ZONE_MAX_SPEED
            pid_max_speed = PID_ZONE_MAX_SPEED
        except NameError:
            # 如果配置文件参数不存在，使用默认值
            approach_threshold = 5
            fine_zone = 15
            slow_zone = 35
            normal_zone = 60
            fine_max_speed = 6
            slow_max_speed = 10
            normal_max_speed = 15
            pid_max_speed = 20

        if abs(horizontal_error) <= approach_threshold:
            # 已经逼近目标，完全停止
            horizontal_speed = 0
            # 清除积分项，避免超调
            self.horizontal_error_sum = 0
            if DEBUG:
                print(f"目标已居中，停止水平舵机: 误差={horizontal_error:.1f}px")
        elif abs(horizontal_error) <= fine_zone:
            # 精细控制区，使用极小的比例控制
            horizontal_speed = horizontal_error * 0.3
            horizontal_speed = max(-fine_max_speed, min(fine_max_speed, horizontal_speed))
            if DEBUG:
                print(f"精细控制区: 误差={horizontal_error:.1f}px, 速度={horizontal_speed:.1f}%")
        elif abs(horizontal_error) <= slow_zone:
            # 减速区，使用小的比例控制
            horizontal_speed = horizontal_error * 0.4
            horizontal_speed = max(-slow_max_speed, min(slow_max_speed, horizontal_speed))
            if DEBUG:
                print(f"减速区控制: 误差={horizontal_error:.1f}px, 速度={horizontal_speed:.1f}%")
        elif abs(horizontal_error) <= normal_zone:
            # 正常控制区，使用中等比例控制
            horizontal_speed = horizontal_error * 0.5
            horizontal_speed = max(-normal_max_speed, min(normal_max_speed, horizontal_speed))
            if DEBUG:
                print(f"正常控制区: 误差={horizontal_error:.1f}px, 速度={horizontal_speed:.1f}%")
        else:
            # 远离目标，使用PID控制但限制输出
            self.horizontal_error_sum += horizontal_error
            # 限制积分项，防止积分饱和
            self.horizontal_error_sum = max(-50, min(50, self.horizontal_error_sum))

            horizontal_derivative = horizontal_error - self.horizontal_last_error

            horizontal_output = (self.horizontal_pid_kp * horizontal_error +
                               self.horizontal_pid_ki * self.horizontal_error_sum +
                               self.horizontal_pid_kd * horizontal_derivative)

            horizontal_speed = max(-pid_max_speed, min(pid_max_speed, horizontal_output))
            if DEBUG:
                print(f"PID控制: 误差={horizontal_error:.1f}px, 输出={horizontal_output:.2f}, 速度={horizontal_speed:.1f}%")

        # 最终安全检查：如果速度很小，强制设为0
        if abs(horizontal_speed) < 0.1:  # 进一步降低阈值，允许更小的速度通过
            horizontal_speed = 0

        self.set_horizontal_speed(horizontal_speed)

        # 调试信息
        if DEBUG:
            print(f"PID调试: V_err={vertical_error:.1f} V_out={vertical_output:.2f} V_angle={self.vertical_current_angle:.1f}")
            print(f"逼近控制: 原始err_x={err_x:.1f}, H_err={horizontal_error:.1f}, H_speed={horizontal_speed:.1f}")
            if err_x > 0:
                print(f"  → 目标在画面右侧，舵机速度={horizontal_speed:.1f}% ({'顺时针' if horizontal_speed > 0 else '逆时针' if horizontal_speed < 0 else '停止'})")
            elif err_x < 0:
                print(f"  → 目标在画面左侧，舵机速度={horizontal_speed:.1f}% ({'顺时针' if horizontal_speed > 0 else '逆时针' if horizontal_speed < 0 else '停止'})")
            else:
                print(f"  → 目标在画面中心，舵机停止")

        # 更新上次误差
        self.vertical_last_error = vertical_error
        self.horizontal_last_error = horizontal_error

    def center_servos(self):
        """两个舵机都回中位"""
        self.set_vertical_angle(90)      # 垂直舵机回90度
        self.set_horizontal_speed(0)     # 水平舵机停止

    def center_vertical(self):
        """垂直舵机回中位"""
        self.set_vertical_angle(90)

    def stop_horizontal(self):
        """停止水平舵机"""
        self.set_horizontal_speed(0)

    def reset_pid(self):
        """重置PID状态"""
        self.vertical_error_sum = 0
        self.horizontal_error_sum = 0
        self.vertical_last_error = 0
        self.horizontal_last_error = 0
        print("PID状态已重置")

    def set_speed_multiplier(self, multiplier):
        """设置全局速度倍数 (0.1-1.0)"""
        self.global_speed_multiplier = max(0.1, min(1.0, multiplier))
        print(f"全局速度倍数设置为: {self.global_speed_multiplier:.2f}")

    def get_speed_multiplier(self):
        """获取当前全局速度倍数"""
        return self.global_speed_multiplier

    def increase_speed(self, step=0.1):
        """增加速度"""
        new_multiplier = min(1.0, self.global_speed_multiplier + step)
        self.set_speed_multiplier(new_multiplier)

    def decrease_speed(self, step=0.1):
        """减少速度"""
        new_multiplier = max(0.1, self.global_speed_multiplier - step)
        self.set_speed_multiplier(new_multiplier)

    def test_servos(self):
        """测试舵机是否正常工作"""
        print("开始测试舵机...")

        # 测试垂直舵机 (180度位置舵机)
        print("测试垂直舵机 (180度位置舵机)...")
        self.set_vertical_angle(45)   # 45度
        time.sleep(1)
        self.set_vertical_angle(135)  # 135度
        time.sleep(1)
        self.set_vertical_angle(90)   # 回中位
        time.sleep(1)

        # 测试水平舵机 (360度连续旋转舵机) - 重点测试停止功能
        print("测试水平舵机 (360度连续旋转舵机)...")
        print("测试小速度控制...")
        self.set_horizontal_speed(10)   # 小速度顺时针
        time.sleep(1)
        self.set_horizontal_speed(0)    # 停止
        time.sleep(2)  # 观察是否真的停止

        self.set_horizontal_speed(-10)  # 小速度逆时针
        time.sleep(1)
        self.set_horizontal_speed(0)    # 停止
        time.sleep(2)  # 观察是否真的停止

        print("测试中等速度...")
        self.set_horizontal_speed(25)   # 中等速度顺时针
        time.sleep(1)
        self.set_horizontal_speed(0)    # 停止
        time.sleep(1)

        print("舵机测试完成 - 请观察水平舵机是否能正确停止")

    def test_servo_direction(self):
        """测试舵机转向是否正确 - 用于调试方向问题"""
        print("\n" + "="*50)
        print("🔧 舵机方向测试 - 请观察舵机转向")
        print("="*50)

        # 回到中位
        print("1. 舵机回中位...")
        self.center_servos()
        time.sleep(2)

        # 测试水平舵机方向
        print("2. 测试水平舵机方向:")
        print("   正速度(+20%) - 应该让摄像头向右看")
        self.set_horizontal_speed(20)
        time.sleep(3)
        self.set_horizontal_speed(0)
        time.sleep(1)

        print("   负速度(-20%) - 应该让摄像头向左看")
        self.set_horizontal_speed(-20)
        time.sleep(3)
        self.set_horizontal_speed(0)
        time.sleep(1)

        # 测试垂直舵机方向
        print("3. 测试垂直舵机方向:")
        print("   45度 - 摄像头应该向上看")
        self.set_vertical_angle(45)
        time.sleep(2)

        print("   135度 - 摄像头应该向下看")
        self.set_vertical_angle(135)
        time.sleep(2)

        print("   90度 - 摄像头回中位")
        self.set_vertical_angle(90)
        time.sleep(1)

        print("="*50)
        print("🔍 方向测试完成！")
        print("如果方向错误，请修改 main.py 中的方向控制逻辑：")
        print("  - 水平方向错误：修改第246行 horizontal_error = err_x 为 horizontal_error = -err_x")
        print("  - 垂直方向错误：修改第217行 vertical_error = -err_y 为 vertical_error = err_y")
        print("="*50)

    def test_horizontal_only(self):
        """专门测试水平舵机是否能动 - 每次转动后回到停止位置"""
        print("\n🔧 水平舵机专项测试")
        print("="*30)
        print("测试模式：转动 → 停止 → 转动 → 停止")

        # 改进的测试序列 - 每次转动后都停止
        test_sequence = [
            (0, "初始停止", 1),
            (10, "小速度顺时针（向左）", 2),
            (0, "停止", 1),
            (-10, "小速度逆时针（向右）", 2),
            (0, "停止", 1),
            (20, "中速度顺时针（向左）", 2),
            (0, "停止", 1),
            (-20, "中速度逆时针（向右）", 2),
            (0, "停止", 1),
            (30, "较快顺时针（向左）", 2),
            (0, "停止", 1),
            (-30, "较快逆时针（向右）", 2),
            (0, "最终停止", 2)
        ]

        for speed, description, duration in test_sequence:
            print(f"\n设置速度: {speed}% ({description})")
            self.set_horizontal_speed(speed)

            if speed > 0:
                print(f"  ← 预期：顺时针转动（向左）")
            elif speed < 0:
                print(f"  → 预期：逆时针转动（向右）")
            else:
                print(f"  ⏸ 预期：完全停止")

            time.sleep(duration)

        print("\n水平舵机专项测试完成")
        print("如果舵机仍不动，可能是硬件问题或PWM设置问题")

    def test_horizontal_pwm_direct(self):
        """直接测试水平舵机PWM占空比 - 最底层测试"""
        print("\n🔧 水平舵机PWM直接测试")
        print("="*35)

        if self.horizontal_pwm is None:
            print("❌ 水平舵机PWM未初始化")
            return

        # 测试不同的PWM占空比
        test_duties = [5.0, 6.0, 7.0, 7.5, 8.0, 9.0, 10.0]

        for duty in test_duties:
            print(f"测试PWM占空比: {duty:.1f}%")
            try:
                self.horizontal_pwm.duty(duty)
                time.sleep(2)
                print(f"  ✓ 设置成功")
            except Exception as e:
                print(f"  ❌ 设置失败: {e}")

        # 回到停止位置
        print("回到停止位置: 7.5%")
        try:
            self.horizontal_pwm.duty(7.5)
        except Exception as e:
            print(f"回到停止位置失败: {e}")

        print("PWM直接测试完成")
        print("如果所有占空比都设置成功但舵机不动，可能是:")
        print("1. 舵机电源问题")
        print("2. 舵机信号线连接问题")
        print("3. 舵机本身故障")
        print("4. PWM频率问题(当前50Hz)")

    def force_horizontal_test(self):
        """强制水平舵机测试 - 绕过所有逻辑直接控制"""
        print("\n🚨 强制水平舵机测试 - 绕过所有PID逻辑")
        print("="*45)

        if self.horizontal_pwm is None:
            print("❌ 水平舵机PWM未初始化")
            return

        print("强制设置不同速度，观察舵机是否转动...")

        # 强制测试序列 - 使用更小的速度范围
        test_sequence = [
            (10, "小速度顺时针"),
            (0, "停止"),
            (-10, "小速度逆时针"),
            (0, "停止"),
            (20, "中等顺时针"),
            (0, "停止"),
            (-20, "中等逆时针"),
            (0, "停止"),
            (30, "较快顺时针"),
            (0, "停止"),
            (-30, "较快逆时针"),
            (0, "停止")
        ]

        for speed, description in test_sequence:
            print(f"强制设置速度: {speed}% ({description})")
            self.set_horizontal_speed(speed)
            time.sleep(3)  # 更长的观察时间

        print("强制测试完成")
        print("如果舵机在此测试中仍不动，问题可能是:")
        print("1. 硬件连接问题")
        print("2. 电源供应问题")
        print("3. 舵机本身故障")

    def calibrate_horizontal_servo(self):
        """校准水平舵机 - 找到正确的PWM范围"""
        print("\n🔧 水平舵机校准程序")
        print("="*35)

        if self.horizontal_pwm is None:
            print("❌ 水平舵机PWM未初始化")
            return

        print("正在校准MG996R 360度舵机...")
        print("请观察舵机转动情况，记录哪些PWM值能让舵机转动")

        # 测试不同的PWM占空比来找到有效范围
        test_duties = [
            (6.0, "强逆时针"),
            (6.5, "中逆时针"),
            (7.0, "弱逆时针"),
            (7.5, "停止位置"),
            (8.0, "弱顺时针"),
            (8.5, "中顺时针"),
            (9.0, "强顺时针")
        ]

        working_duties = []

        for duty, description in test_duties:
            print(f"\n测试PWM占空比: {duty:.1f}% ({description})")
            try:
                self.horizontal_pwm.duty(duty)
                time.sleep(3)

                # 询问用户舵机是否转动
                print(f"  PWM {duty:.1f}% - 舵机是否转动？")
                if duty == 7.5:
                    print("  (这应该是停止位置)")
                elif duty < 7.5:
                    print("  (这应该是逆时针转动)")
                else:
                    print("  (这应该是顺时针转动)")

                working_duties.append((duty, description))

            except Exception as e:
                print(f"  ❌ 设置PWM {duty:.1f}%失败: {e}")

        # 回到停止位置
        print("\n回到停止位置...")
        try:
            self.horizontal_pwm.duty(7.5)
        except Exception as e:
            print(f"回到停止位置失败: {e}")

        print("\n校准完成！")
        print("根据测试结果，建议的PWM设置：")
        print("- 停止: 7.5%")
        print("- 顺时针范围: 7.6% - 8.5%")
        print("- 逆时针范围: 6.5% - 7.4%")
        print("\n如果舵机只能单向转动，可能需要调整PWM范围或检查舵机类型")

    def test_pid_control(self):
        """测试PID控制是否正常工作"""
        print("\n🔧 PID控制测试")
        print("="*25)

        # 模拟不同的误差值来测试PID响应
        test_errors = [
            (50, 0, "大误差向右"),
            (-50, 0, "大误差向左"),
            (20, 0, "中等误差向右"),
            (-20, 0, "中等误差向左"),
            (10, 0, "小误差向右"),
            (-10, 0, "小误差向左"),
            (0, 0, "无误差")
        ]

        for err_x, err_y, description in test_errors:
            print(f"\n测试: {description} (err_x={err_x}, err_y={err_y})")
            self.pid_control(err_x, err_y)
            time.sleep(2)

        print("\nPID控制测试完成")

    def cleanup(self):
        """清理资源"""
        try:
            if self.vertical_pwm:
                self.vertical_pwm.duty(7.5)  # 垂直舵机回中位
            if self.horizontal_pwm:
                self.horizontal_pwm.duty(7.5)  # 水平舵机停止
            print("舵机控制器资源已清理 - 所有舵机已停止")
        except Exception as e:
            print(f"清理舵机资源时出错: {e}")

DEBUG=True  # 临时启用调试模式，查看PID控制输出
PRINT_TIME = False
debug_draw_err_line = False
debug_draw_err_msg = False
debug_draw_circle = False
debug_draw_rect = False
debug_show_hires = False

################################ config #########################################

# DEBUG=True                 # 打开调试模式，取消注释即可
# PRINT_TIME = True          # 打印每一步消耗的时间，取消注释即可
debug_draw_err_line = True   # 画出圆心和画面中心的误差线，需要消耗1ms左右时间
# debug_draw_err_msg = True    # 画出圆心和画面中心的误差值和 FPS 信息，需要消耗7ms左右时间，慎用
debug_draw_circle = True       # 画出圆圈，实际是画点，需要再打开变量, debug 模式都会画，耗费时间比较多，慎用
# debug_draw_rect = True         # 画出矩形框
debug_show_hires = True        # 显示结果在高分辨率图上，而不是小分辨率图上， 开启了 hires_mode 才生效


crop_padding = 12            # 裁切图时的外扩距离，调试到保证最近和最远位置整个黑框在检测框里，可以打开 DEBUG 模式看
rect_min_limit = 12          # 找到的大黑边框四个点最小距离必须大于这个值才有效，防止找到错误的值，可以放到最远位置测试
std_from_white_rect = True   # 裁切标准图是裁切自A4纸内部白色部分（更精准），False则是带黑框的外围框（整个A4纸）（更快一点点）
circle_num_points = 50       # 生成的第三个圆圈的点数量，控制圆边的平滑程度，可以用来巡迹
std_res = [int(29.7 / 21 * 80), 80]        # 找中心点和圆圈的分辨率，越大越精确，更慢，A4 29.7 x 21cm
hires_mode = True           # 高分辨模式，适合 find_circle 模式使用，帧率会更低但是找圆圈更精准
                             # 不 find_circle 也可以使用，找4个角点更精准，需要配合设置合理的 std_res
                             # 注意开启了这个模式，输出的误差值也是基于大图的分辨率
high_res = 448               # 高分辨率模式宽高,越高越清晰但是帧率越低，注意 std_res 也要跟着改大点
model_path = "/root/models/model_3356.mud" # 检测黑框模型路径，从 https://maixhub.com/model/zoo/1159 下载并传到开发板的 /root/models 目录


find_circle = False          # 在找到黑框以内白框后是否继续找圆，如果圆圈画得标准，在纸正中心则不用找，如果画点不在纸正中心则需要找。
                             # 建议把A4纸制作正确就不用找了，帧率更高。
                             # 可以用hires_mode 更清晰才能识别到，另外设置合理的 std_res
cam_buff_num = 1             # 摄像头缓冲， 1 延迟更低帧率慢一点点， 2延迟更高帧率高一点点
find_laser = False           # 找激光点（未测试），实际使用时直接把摄像头中心和激光点保持移植就好了，不需要找激光点

auto_awb = True                            # 自动白平衡或者手动白平衡
awb_gain = [0.134, 0.0625, 0.0625, 0.1139]  # 手动白平衡，auto_awb为False才生效， R GR GB B 的值，调 R 和 B 即可
contrast = 80                               # 对比度，会影响到检测，阴影和圆圈痕迹都会更重

# 舵机控制配置
# 舵机连接说明：
# - 垂直舵机（180°版本MG996R）连接到 A19口（PWM7）- 控制上下俯仰（位置控制）
# - 水平舵机（360°版本MG996R）连接到 A18口（PWM6）- 控制左右旋转（速度控制）
# 注意：混合控制方式 - 垂直位置控制 + 水平速度控制
servo_control_enabled = True               # 是否启用舵机控制

# 垂直舵机PID参数（180度位置舵机）- 使用内嵌配置参数
servo_vertical_pid_kp = VERTICAL_PID_KP    # 垂直比例系数
servo_vertical_pid_ki = VERTICAL_PID_KI    # 垂直积分系数
servo_vertical_pid_kd = VERTICAL_PID_KD    # 垂直微分系数

# 水平舵机PID参数（360度速度舵机）- 使用内嵌配置参数
servo_horizontal_pid_kp = HORIZONTAL_PID_KP  # 水平比例系数
servo_horizontal_pid_ki = HORIZONTAL_PID_KI  # 水平积分系数
servo_horizontal_pid_kd = HORIZONTAL_PID_KD  # 水平微分系数

servo_error_threshold = 5                  # 误差阈值，小于此值不进行舵机调整（降低阈值，让PID更容易启动）
servo_max_no_target_frames = 30           # 最大未检测到目标的帧数，超过后停止舵机
servo_test_mode = False                   # 测试模式：关闭测试模式，使用正常逼近控制

# 舵机速度调节参数 - 使用内嵌配置参数
servo_speed_multiplier = GLOBAL_SPEED_MULTIPLIER      # 全局速度倍数
servo_approach_speed_limit = 10                       # 逼近时的最大速度限制（像素/帧）

###################################################################################

if not os.path.exists(model_path):
    model_path1 = "model/model_3356.mud"
    if not os.path.exists(model_path1):
        print(f"load model failed, please put model in {model_path}, or {os.path.getcwd()}/{model_path1}")
    model_path = model_path1

# 初始化AI检测器
detector = nn.YOLOv5(model=model_path, dual_buff = True)

# 初始化舵机控制器
servo_controller = ServoController()
# 设置分离的PID参数
servo_controller.vertical_pid_kp = servo_vertical_pid_kp
servo_controller.vertical_pid_ki = servo_vertical_pid_ki
servo_controller.vertical_pid_kd = servo_vertical_pid_kd
servo_controller.horizontal_pid_kp = servo_horizontal_pid_kp
servo_controller.horizontal_pid_ki = servo_horizontal_pid_ki
servo_controller.horizontal_pid_kd = servo_horizontal_pid_kd
servo_controller.test_mode = servo_test_mode

# 设置全局速度倍数
servo_controller.set_speed_multiplier(servo_speed_multiplier)

print(f"舵机控制器初始化完成")
print(f"当前速度设置: {servo_controller.get_speed_multiplier():.2f}")
print(f"提示: 如果舵机移动太快，可以调小 servo_speed_multiplier 参数（当前: {servo_speed_multiplier}）")
print(f"提示: 如果舵机移动太慢，可以调大 servo_speed_multiplier 参数（最大: 1.0）")

# 初始化摄像头
if hires_mode:
    cam = camera.Camera(high_res, high_res, detector.input_format(), buff_num=cam_buff_num)
else:
    cam = camera.Camera(detector.input_width(), detector.input_height(), detector.input_format(), buff_num=cam_buff_num)
if not auto_awb:
    cam.awb_mode(camera.AwbMode.Manual)
    cam.set_wb_gain(awb_gain)
cam.constrast(contrast)
# cam.set_windowing([448, 448])

# 舵机初始化位置
servo_controller.center_servos()  # 两个舵机都回中位

# 舵机校准和测试选项
print("\n" + "="*50)
print("🔧 MG996R 360度舵机问题诊断和修复")
print("="*50)
print("检测到你的水平舵机可能存在以下问题：")
print("1. 只能向一个方向转动")
print("2. 需要红线在黑框内才能转动")
print("3. 转动速度与垂直舵机不匹配")
print("\n已应用以下修复：")
print("✓ 调整PWM范围：顺时针8.5%，逆时针6.5%，停止7.5%")
print("✓ 增大误差阈值到15px，允许更大范围控制")
print("✓ 优化速度控制算法")
print("\n是否需要进行舵机校准测试？")
print("输入选项：")
print("  1 - 跳过测试，直接运行程序")
print("  2 - 快速测试水平舵机")
print("  3 - 完整校准程序")
print("  4 - 强制测试（绕过所有逻辑）")

# 简单的用户输入（在实际MaixCAM上可能需要其他方式）
test_choice = "1"  # 默认跳过测试，如需测试请修改这里

if test_choice == "2":
    print("\n执行快速测试...")
    servo_controller.test_horizontal_only()
elif test_choice == "3":
    print("\n执行完整校准...")
    servo_controller.calibrate_horizontal_servo()
elif test_choice == "4":
    print("\n执行强制测试...")
    servo_controller.force_horizontal_test()
elif test_choice == "5":
    print("\n执行PID控制测试...")
    servo_controller.test_pid_control()
else:
    print("\n跳过测试，直接运行程序...")

# 由于你反映PID控制下舵机不动，我们先测试PID控制
print("\n🚨 检测到PID控制问题，先执行PID测试...")
servo_controller.test_pid_control()

print("="*50)

def find_laser_point(img, original_img):
    '''
        随便写的，有需要请自己修改算法
    '''

    # 这里需要调阈值
    ths = [[0, 100, -128, 127, -128, -18]]
    blobs = img_std.find_blobs(ths, x_stride=2, y_stride=2)
    max_s = 0
    max_b = None
    for b in blobs:
        s = b.w() * b.h()
        if s > max_s:
            max_s = s
            max_b = b
    if DEBUG:
        laser_binary = img.binary(ths, copy=True)
        original_img.draw_image(original_img.width() - laser_binary.width(), original_img.height() - laser_binary.height(), laser_binary)
    return max_b

_t = time.ticks_ms()
def debug_time(msg):
    if PRINT_TIME:
        global _t
        print(f"t: {time.ticks_ms() - _t:4d} {msg}")
        _t = time.ticks_ms()

err_center = [0, 0] # 距离中心的误差
center_pos = [cam.width() // 2, cam.height() // 2] # 画面的中心
last_center = center_pos # 上一次检测到的圆心距离
last_center_small = [detector.input_width(), detector.input_height()] # 高清模式时，在小图的中心坐标

# 舵机控制相关变量
no_target_count = 0          # 未检测到目标的计数
target_lost = False          # 目标丢失标志

while not app.need_exit():
    debug_time("start")
    img = cam.read()
    debug_time("cam read")
    # AI 检测外框
    if hires_mode:
        img_ai = img.resize(detector.input_width(), detector.input_height())
    else:
        img_ai = img # new copy
    debug_time("resize")
    objs = detector.detect(img_ai, conf_th = 0.5, iou_th = 0.45)
    max_idx = -1
    max_s = 0
    for i, obj in enumerate(objs):
        s = obj.w * obj.h
        if s > max_s:
            max_s = s
            max_idx = i
        # img_ai.draw_rect(obj.x, obj.y, obj.w, obj.h, color = image.COLOR_RED, thickness=4)
        # msg = f'{detector.labels[obj.class_id]}: {obj.score:.2f}'
        # img_ai.draw_string(obj.x, obj.y, msg, color = image.COLOR_RED, scale=2)
    debug_time("detect")
    if max_idx >= 0:
        obj = objs[max_idx]
        w = obj.w + crop_padding * 2
        h = obj.h + crop_padding * 2
        w = w + 1 if w % 2 != 0 else w
        h = h + 1 if h % 2 != 0 else h
        x = obj.x - crop_padding
        y = obj.y - crop_padding
        if x < 0:
            w += x
            x = 0
        if y < 0:
            h += y
            y = 0
        if x + w > img_ai.width():
            w = img_ai.width() - x
        if y + h > img_ai.height():
            h = img_ai.height() - y
        crop_ai = img_ai.crop(x, y, w, h)
        crop_ai_rect = [x, y, w, h]
        # 算出裁切范围对应在大图的位置
        # 注意这里只考虑到了拉伸缩放(iamge.Fit.FILL)
        img_ai_scale = [img.width() / img_ai.width(), img.height() / img_ai.height()]
        # crop_rect = image.resize_map_pos_reverse(img.width(), img.height(), img_ai.width(), img_ai.height(), image.Fit.FIT_FILL, obj.x, obj.y, obj.w, obj.h)
        crop_rect = [int(obj.x * img_ai_scale[0]), int(obj.y * img_ai_scale[1]), int(obj.w * img_ai_scale[0]), int(h * img_ai_scale[0])]
        img_cv = image.image2cv(img, False, False)
        crop_ai_cv = image.image2cv(crop_ai, False, False)
        debug_time("crop")

        gray = crop_ai.to_format(image.Format.FMT_GRAYSCALE)
        gray_cv = image.image2cv(gray, False, False)
        debug_time("gray")

        # 二值化图，找出黑色外轮廓，可以用其它算法
        # 高斯模糊去噪声
        # blurred = cv2.GaussianBlur(gray_cv, (5, 5), 0)
        # 边缘检测，阈值 0，150
        # edged = cv2.Canny(blurred, 50, 150)
        # # 膨胀处理
        # kernel = np.ones((5, 5), np.uint8)
        # dilated = cv2.dilate(edged, kernel, iterations=1)
        # # 腐蚀处理
        # binary = cv2.erode(dilated, kernel, iterations=1)
        # 自适应二值化，最后两个参数可以调整
        binary = cv2.adaptiveThreshold(gray_cv, 255,
                       cv2.ADAPTIVE_THRESH_MEAN_C,
                       cv2.THRESH_BINARY_INV, 27, 31)
        debug_time("binary")


        if std_from_white_rect:
            # 执行洪泛填充找出内白色轮廓
            h, w = binary.shape[:2]
            mask = np.zeros((h + 2, w + 2), np.uint8)
            # 设置种子点（左上角和右下角），如果环境好，可以只点一个角
            seed_point = (2, 2)
            seed_point2 = (w - 2, h - 2)
            # 设置填充值（白色 255）
            fill_value = 255
            # 执行洪泛填充（以左上角像素值为基准）
            cv2.floodFill(binary, mask, seed_point, fill_value, loDiff=5, upDiff=5, flags=4)
            cv2.floodFill(binary, mask, seed_point2, fill_value, loDiff=5, upDiff=5, flags=4)
            binary = cv2.bitwise_not(binary)
            debug_time("fill")

        # 查找轮廓4个角点
        approx = None
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if len(contours) > 0:
            # 筛选出最大的轮廓
            largest_contour = max(contours, key=cv2.contourArea)
            # 近似多边形
            epsilon = 0.02 * cv2.arcLength(largest_contour, True)
            approx = cv2.approxPolyDP(largest_contour, epsilon, True)
            debug_time("find countours")
            # 如果找到的是一个四边形
            if len(approx) == 4:
                # 获取矩形四个角点
                # 对角点进行排序：左上、右上、右下、左下
                corners = approx.reshape((4, 2))
                # 按顺序排列角点（左上、右上、右下、左下）
                rect = np.zeros((4, 2), dtype="float32")
                s = corners.sum(axis=1)
                rect[0] = corners[np.argmin(s)] # 最小和，左上
                rect[2] = corners[np.argmax(s)] # 最大和，右下
                diff = np.diff(corners, axis=1) # y - x
                rect[3] = corners[np.argmax(diff)] # 差最大，左下
                rect[1] = corners[np.argmin(diff)] # 差最小，右上
                minW = min(rect[1][0] - rect[0][0], rect[2][0] - rect[3][0])
                minH = min(rect[3][1] - rect[0][1], rect[2][1] - rect[1][1])
                if minH > rect_min_limit and minW > rect_min_limit:
                    debug_time("find rect")

                    # 计算目标图像宽高（按最大边计算）
                    # (tl, tr, br, bl) = rect
                    # widthA = np.linalg.norm(br - bl)
                    # widthB = np.linalg.norm(tr - tl)
                    # maxWidth = int(max(widthA, widthB) * img_ai_scale[0] * std_scale)

                    # heightA = np.linalg.norm(tr - br)
                    # heightB = np.linalg.norm(tl - bl)
                    # maxHeight = int(max(heightA, heightB) * img_ai_scale[1] * std_scale)
                    # print(maxWidth, maxHeight)


                    maxWidth = std_res[0]
                    maxHeight = std_res[1]

                    # rect 映射到大图, 从大图中得到标准内框图
                    rect[:, 0] += crop_ai_rect[0]
                    rect[:, 1] += crop_ai_rect[1]
                    rect[:, 0] *= img_ai_scale[0]
                    rect[:, 1] *= img_ai_scale[1]
                    # 透视变换
                    dst = np.array([
                        [0, 0],
                        [maxWidth - 1, 0],
                        [maxWidth - 1, maxHeight - 1],
                        [0, maxHeight - 1]], dtype="float32")
                    M = cv2.getPerspectiveTransform(rect, dst)
                    M_inv = np.linalg.inv(M)
                    img_std_cv = cv2.warpPerspective(img_cv, M, (maxWidth, maxHeight))
                    img_std = image.cv2image(img_std_cv, False, False)
                    debug_time("get std img")

                    # 如果前面找到得标准图有黑框，用find_blobs 处理一下
                    # ths = [[0, 10, -128, 127, -128, 127]]
                    # blobs = img_std.find_blobs(ths, roi=[0, 0, 10, 10], x_stride=1, y_stride=1)
                    # A4 纸 21cm, 黑框 1.8*2=3.6cm， 白色区域为 17.4cm，圆圈2cm间距
                    # 得出 圆圈间距像素为 2/17.4 * 白色区域高度像素。（0.1149425287356322）
                    # 如果是黑色边框，则 2/21 * 黑框高度像素。(0.09523809523809523)
                    # if len(blobs) > 0: # 有黑框
                        # circle_dist = img_std.height() * 0.09523809523809523
                    # else:
                    if std_from_white_rect:
                        circle_dist = int(img_std.height() * 0.1149425287356322)
                    else:
                        circle_dist = img_std.height() * 0.09523809523809523
                    if circle_dist > 0:
                        center = [img_std.width() // 2, img_std.height() // 2]
                        # 是否找圆和圆心
                        center_new = None
                        if find_circle:
                            img_std_gray_cv = cv2.cvtColor(img_std_cv, cv2.COLOR_RGB2GRAY)
                            w = h = int(circle_dist * 3)
                            roi = [center[0] - w // 2, center[1] - h // 2, w, h]
                            img_small_circle_cv = img_std_gray_cv[roi[1]:roi[1] + roi[3], roi[0]:roi[0]+roi[2]]
                            if DEBUG:
                                img_small_circle = image.cv2image(img_small_circle_cv, False, False)
                                img.draw_image(crop_ai.width(), img_std.height(), img_small_circle)

                            # 用霍夫变换找圆
                            circles = cv2.HoughCircles(img_small_circle_cv, cv2.HOUGH_GRADIENT, dp=1.2,
                                                    minDist=roi[2] // 2,
                                                    param1=100, param2=20,
                                                    minRadius=roi[2] // 4, maxRadius=roi[2] // 2)
                            # 把找圆范围画出来
                            if DEBUG:
                                img_std.draw_rect(roi[0], roi[1], roi[2], roi[3], image.COLOR_ORANGE)
                                cv2.circle(img_std_cv, center, 1, (0, 255, 0), -1)
                            # 若检测到圆，得到中心和半径
                            circle_dist_new = 0
                            if circles is not None:
                                circles = np.uint16(np.around(circles))
                                for c in circles[0, :]:
                                    center_new = (c[0] + roi[0], c[1] + roi[1])  # 圆心坐标偏移回原图
                                    circle_dist_new = c[2]
                                    if DEBUG:
                                        cv2.circle(img_std_cv, center_new, circle_dist_new, (0, 255, 0), 1)
                                        cv2.circle(img_std_cv, center_new, 1, (0, 0, 255), 3)  # 圆心
                                    # 这里认为只能检测到一个圆，如果多个，那画面有问题，或者再优化这里的代码
                                    break
                            # binary = cv2.adaptiveThreshold(img_std_gray_cv, 255,
                            #                cv2.ADAPTIVE_THRESH_MEAN_C,
                            #                cv2.THRESH_BINARY_INV, 11, 3)
                            # # 膨胀加强线条
                            # kernel = np.ones((2, 2), np.uint8)
                            # enhanced = cv2.dilate(binary, kernel, iterations=1)
                            # eroded = cv2.erode(enhanced, kernel, iterations=1)
                            # circles = img3.find_circles(roi = roi, x_stride=4, y_stride = 4, threshold=2000, r_step = 4)
                            if center_new:
                                # 更新圆环中心和圆环间距离
                                center = center_new
                                circle_dist = circle_dist_new
                                # 在标准图中画出新中心和第三个圈
                                if DEBUG:
                                    cv2.circle(img_std_cv, center, 1, (0, 255, 0), -1)
                                    cv2.circle(img_std_cv, center, circle_dist * 3, (0, 255, 0), 1)
                            debug_time("find circle")

                            # 如果不找圆心，或者找到了圆心
                        if (not find_circle) or (center_new):
                            # 原图画圆中心
                            std_center_points = np.array([[center]], dtype=np.float32)
                            original_center_point = cv2.perspectiveTransform(std_center_points, M_inv)[0][0].astype(np.int32).tolist()
                            err_center = [
                                original_center_point[0] - center_pos[0],
                                original_center_point[1] - center_pos[1],
                            ]
                            last_center = original_center_point
                            last_center_small = [int(last_center[0] / img_ai_scale[0]), int(last_center[1] / img_ai_scale[1])]

                            # 舵机控制逻辑
                            if servo_control_enabled:
                                # 如果目标重新出现，重置PID状态
                                if target_lost:
                                    servo_controller.reset_pid()
                                    target_lost = False

                                # 计算误差大小
                                error_magnitude = math.sqrt(err_center[0]**2 + err_center[1]**2)

                                if error_magnitude > servo_error_threshold:
                                    # 使用PID控制舵机进行逼近
                                    servo_controller.pid_control(err_center[0], err_center[1])
                                    no_target_count = 0  # 重置未检测到目标的计数

                                    if DEBUG:
                                        print(f"舵机逼近: 误差=({err_center[0]:.1f}, {err_center[1]:.1f}), 误差大小={error_magnitude:.1f}")
                                else:
                                    # 误差较小，目标已居中，强制停止水平舵机
                                    servo_controller.stop_horizontal()
                                    # 强制清除PID积分项
                                    servo_controller.horizontal_error_sum = 0
                                    if DEBUG:
                                        print(f"目标已居中，强制停止: 误差大小={error_magnitude:.1f}px")
                            else:
                                # 舵机控制被禁用，停止舵机
                                servo_controller.stop_horizontal()
                            # 原图画圆
                            radius = circle_dist * 3 # 第三个圈的半径
                            # 构造圆上的轮廓点
                            debug_time("get points 3")
                            angles = np.linspace(0, 2 * np.pi, circle_num_points, endpoint=False)  # endpoint=False 避免首尾重复
                            cos_vals = np.cos(angles)
                            sin_vals = np.sin(angles)

                            # 向量方式生成所有点
                            x = center[0] + radius * cos_vals
                            y = center[1] + radius * sin_vals
                            circle_pts = np.stack((x, y), axis=1).astype(np.float32)  # shape: (N, 2)
                            circle_pts = circle_pts[np.newaxis, :, :]  # reshape to (1, N, 2)
                            debug_time("get points 1")

                            # 反变换回原图
                            orig_circle_pts = cv2.perspectiveTransform(circle_pts, M_inv)
                            debug_time("get points")

                            # 找激光点
                            original_lasert_point = None
                            if find_laser:
                                laser_point = find_laser_point(img_std, img if DEBUG else img_ai)
                                if laser_point:
                                    # 原图坐标
                                    points = np.array([[[laser_point.x(), laser_point.y()]]], dtype=np.float32)
                                    original_lasert_point = cv2.perspectiveTransform(points, M_inv)[0][0]
                            # 画在大图上
                            if DEBUG or debug_show_hires:
                                img.draw_circle(original_center_point[0], original_center_point[1], 4, image.COLOR_RED, thickness=-1)
                                pts = np.round(orig_circle_pts[0]).astype(np.int32)
                                cv2.polylines(img_cv, [pts], isClosed=True, color=(0, 0, 255), thickness=1)
                                if original_lasert_point is not None:
                                    img.draw_circle(original_lasert_point[0], original_lasert_point[1], 3, image.COLOR_GREEN, thickness=1)
                            else:
                            # 画在小图上显示
                                # too slow
                                # center_ai = image.resize_map_pos(img.width(), img.height(), img_ai.width(), img_ai.height(), image.Fit.FIT_FILL, original_center_point[0], original_center_point[1])
                                center_ai = [int(original_center_point[0] * img_ai_scale[0]), int(original_center_point[1] * img_ai_scale[1])]
                                img_ai.draw_circle(center_ai[0], center_ai[1], 2, image.COLOR_RED, thickness=-1)
                                pts = orig_circle_pts[0]  # shape: (N, 2)

                                scaled_pts = (pts * img_ai_scale).astype(np.int32)  # shape: (N, 2)
                                points = scaled_pts.reshape(-1).tolist()  # 转为 Python list（与原结果相同）
                                if debug_draw_circle:
                                    img_ai.draw_keypoints(points, image.COLOR_RED, 1, line_thickness=1)
                                if original_lasert_point is not None:
                                    img_ai.draw_circle(original_lasert_point[0], original_lasert_point[1], 3, image.COLOR_GREEN, thickness=1)
                            debug_time("draw points")
                        if DEBUG:
                            img.draw_image(crop_ai.width(), 0, img_std)
                    else:
                        print("detected circle too small", img_std.width(), img_std.height())
                else:
                    print(minW, minH, "rect not valid")
                    # 未检测到有效矩形，增加未检测计数
                    if servo_control_enabled:
                        no_target_count += 1
                        if no_target_count > servo_max_no_target_frames:
                            servo_controller.center_servos()  # 回到中位
                            target_lost = True  # 设置目标丢失标志
                            if DEBUG:
                                print("长时间未检测到目标，舵机回中位")
            else:
                # 未检测到任何目标，增加未检测计数
                if servo_control_enabled:
                    no_target_count += 1
                    if no_target_count > servo_max_no_target_frames:
                        servo_controller.center_servos()  # 回到中位
                        target_lost = True  # 设置目标丢失标志
                        if DEBUG:
                            print("长时间未检测到目标，舵机回中位")

        # 绘制路径
        if approx is not None:
            cv2.drawContours(crop_ai_cv, [approx], -1, (255, 255, 255), 1)
        if DEBUG:
            img.draw_image(0, 0, crop_ai)
            img2 = image.cv2image(binary, False, False)
            img.draw_image(0, crop_ai.height(), img2)

        if debug_draw_rect:
            img.draw_rect(crop_rect[0], crop_rect[1], crop_rect[2], crop_rect[3], color = image.COLOR_RED, thickness=2)
            # msg = f'{detector.labels[obj.class_id]}: {obj.score:.2f}'
            # img.draw_string(obj.x, obj.y, msg, color = image.COLOR_RED, scale=2)
        debug_time("draw")
    if DEBUG or debug_show_hires:
        if debug_draw_err_line:
            img.draw_line(center_pos[0], center_pos[1], last_center[0], last_center[1], image.COLOR_RED, thickness=3)
        if debug_draw_err_msg:
            # 显示误差信息和舵机状态
            status_text = f"err: {err_center[0]:5.1f}, {err_center[1]:5.1f}, fps: {time.fps():2.0f}"
            if servo_control_enabled:
                # 计算当前误差大小
                current_error = math.sqrt(err_center[0]**2 + err_center[1]**2)
                if current_error <= servo_error_threshold:
                    status_text += f", 目标居中({current_error:.0f}px), V:{servo_controller.vertical_current_angle:.0f}°"
                else:
                    status_text += f", 逼近中({current_error:.0f}px), V:{servo_controller.vertical_current_angle:.0f}°"
            else:
                status_text += ", servo: OFF"
            img.draw_string(2, img.height() - 32, status_text, image.COLOR_RED, scale=1.5, thickness=2)
        disp.show(img)
    else:
        if debug_draw_err_line:
            img_ai.draw_line(center_pos[0], center_pos[1], last_center_small[0], last_center_small[1], image.COLOR_RED, thickness=3)
        if debug_draw_err_msg:
            # 显示误差信息和舵机状态
            status_text = f"err: {err_center[0]:5.1f}, {err_center[1]:5.1f}, fps: {time.fps():2.0f}"
            if servo_control_enabled:
                # 计算当前误差大小
                current_error = math.sqrt(err_center[0]**2 + err_center[1]**2)
                if current_error <= servo_error_threshold:
                    status_text += f", 目标居中({current_error:.0f}px), V:{servo_controller.vertical_current_angle:.0f}°"
                else:
                    status_text += f", 逼近中({current_error:.0f}px), V:{servo_controller.vertical_current_angle:.0f}°"
            else:
                status_text += ", servo: OFF"
            img_ai.draw_string(2, img.height() - 32, status_text, image.COLOR_RED, scale=1.5, thickness=2)
        disp.show(img_ai)
    debug_time("display img")

# 程序退出时清理资源
print("程序退出，清理资源...")
servo_controller.cleanup()
print("程序已退出")