#!/usr/bin/env python3
"""
main.py调试脚本
专门测试为什么在main.py中水平舵机不工作

@author: AI Assistant
@date: 2025.8.1
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import ServoController
import time

def test_main_servo_controller():
    """测试main.py中的ServoController类"""
    print("🔧 测试main.py中的ServoController")
    print("="*40)
    
    try:
        # 创建ServoController实例（与main.py完全一致）
        servo_controller = ServoController()
        print("✅ ServoController初始化成功")
        
        # 测试参数
        print(f"全局速度倍数: {servo_controller.speed_scale_factor}")
        print(f"水平PWM参数: 停止={servo_controller.horizontal_stop_duty}, 顺时针={servo_controller.horizontal_cw_duty}, 逆时针={servo_controller.horizontal_ccw_duty}")
        
        # 测试直接速度设置
        print("\n1. 测试直接速度设置:")
        test_speeds = [10, -10, 20, -20, 0]
        
        for speed in test_speeds:
            print(f"  设置速度: {speed}%")
            servo_controller.set_horizontal_speed(speed)
            time.sleep(2)
        
        # 测试PID控制
        print("\n2. 测试PID控制:")
        test_errors = [
            (30, 0, "大误差向右"),
            (-30, 0, "大误差向左"),
            (10, 0, "小误差向右"),
            (-10, 0, "小误差向左"),
            (0, 0, "无误差")
        ]
        
        for err_x, err_y, description in test_errors:
            print(f"  测试: {description} (err_x={err_x}, err_y={err_y})")
            servo_controller.pid_control(err_x, err_y)
            time.sleep(3)
            
            # 停止舵机
            servo_controller.stop_horizontal()
            time.sleep(1)
        
        print("\n3. 测试误差阈值逻辑:")
        # 模拟main.py中的误差阈值检查
        servo_error_threshold = 1  # 与main.py保持一致
        
        test_error_cases = [
            (2, 0, "误差=2px"),
            (1, 0, "误差=1px"),
            (0.5, 0, "误差=0.5px"),
            (5, 0, "误差=5px"),
            (10, 0, "误差=10px")
        ]
        
        for err_x, err_y, description in test_error_cases:
            import math
            error_magnitude = math.sqrt(err_x**2 + err_y**2)
            
            print(f"  {description}: 误差大小={error_magnitude:.1f}px")
            
            if error_magnitude > servo_error_threshold:
                print(f"    ✅ 启动PID控制 (>{servo_error_threshold}px)")
                servo_controller.pid_control(err_x, err_y)
            else:
                print(f"    ⏸ 停止舵机 (<={servo_error_threshold}px)")
                servo_controller.stop_horizontal()
            
            time.sleep(2)
        
        # 清理
        servo_controller.cleanup()
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_speed_scaling():
    """测试速度缩放逻辑"""
    print("\n🔍 测试速度缩放逻辑")
    print("="*25)
    
    # 模拟main.py中的速度缩放
    GLOBAL_SPEED_MULTIPLIER = 0.4
    speed_scale_factor = 1.0
    
    test_speeds = [5, 10, 15, 20, -5, -10, -15, -20]
    
    for original_speed in test_speeds:
        # 应用缩放（与main.py中set_horizontal_speed一致）
        speed = max(-100, min(100, original_speed))
        speed = speed * speed_scale_factor * GLOBAL_SPEED_MULTIPLIER
        
        # 速度过滤
        if abs(speed) < 0.1:
            final_speed = 0
            filtered = "是"
        else:
            final_speed = speed
            filtered = "否"
        
        print(f"  原始速度: {original_speed:3.0f}% → 缩放后: {speed:5.2f}% → 最终: {final_speed:5.2f}% (被过滤: {filtered})")

def check_pwm_calculation():
    """检查PWM占空比计算"""
    print("\n📊 检查PWM占空比计算")
    print("="*25)
    
    # main.py中的PWM参数
    horizontal_stop_duty = 7.5
    horizontal_cw_duty = 8.5
    horizontal_ccw_duty = 6.5
    
    test_speeds = [1, 2, 5, 10, 20, -1, -2, -5, -10, -20]
    
    for speed in test_speeds:
        if speed == 0:
            duty = horizontal_stop_duty
        elif speed > 0:
            duty_range = horizontal_cw_duty - horizontal_stop_duty
            duty_change = (abs(speed) / 100.0) * duty_range
            duty = horizontal_stop_duty + duty_change
            duty = min(duty, horizontal_cw_duty)
        else:
            duty_range = horizontal_stop_duty - horizontal_ccw_duty
            duty_change = (abs(speed) / 100.0) * duty_range
            duty = horizontal_stop_duty - duty_change
            duty = max(duty, horizontal_ccw_duty)
        
        change = duty - horizontal_stop_duty
        print(f"  速度: {speed:3.0f}% → PWM: {duty:.3f}% (变化: {change:+.3f}%)")

def main():
    """主函数"""
    print("🔧 main.py水平舵机调试工具")
    print("="*35)
    
    try:
        test_speed_scaling()
        check_pwm_calculation()
        test_main_servo_controller()
        
        print("\n" + "="*35)
        print("🏁 调试完成！")
        print("\n📋 如果舵机还是不动，可能的原因：")
        print("1. 速度被过度缩放（检查GLOBAL_SPEED_MULTIPLIER）")
        print("2. 误差阈值设置不当（检查servo_error_threshold）")
        print("3. PID参数过小（检查PID系数）")
        print("4. PWM变化幅度不够（检查PWM范围）")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
