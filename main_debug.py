#!/usr/bin/env python3
"""
main.py调试脚本
专门测试为什么在main.py中水平舵机不工作

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time
import math

class SimpleServoController:
    """简化的舵机控制器，复制main.py中的关键逻辑"""
    def __init__(self):
        try:
            # 配置引脚
            pinmap.set_pin_function("A18", "PWM6")  # 水平舵机

            # 初始化PWM
            self.horizontal_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
            print("✓ 水平舵机PWM6初始化成功")

            # 水平舵机参数（与main.py保持一致）
            self.horizontal_stop_duty = 7.5
            self.horizontal_cw_duty = 9.0      # 增大范围
            self.horizontal_ccw_duty = 6.0     # 增大范围

            # PID参数（与main.py保持一致）
            self.horizontal_pid_kp = 0.06
            self.horizontal_pid_ki = 0.001
            self.horizontal_pid_kd = 0.008

            # PID状态变量
            self.horizontal_error_sum = 0
            self.horizontal_last_error = 0

            # 速度控制参数（与main.py保持一致）
            self.global_speed_multiplier = 1.5  # 增大到1.5
            self.speed_scale_factor = 1.0

        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            self.horizontal_pwm = None

    def set_horizontal_speed(self, speed):
        """设置水平舵机速度（与main.py完全一致）"""
        if self.horizontal_pwm is None:
            print("警告：水平舵机PWM未初始化")
            return

        original_speed = speed
        speed = max(-100, min(100, speed))
        speed = speed * self.speed_scale_factor * self.global_speed_multiplier

        print(f"🔧 速度处理: {original_speed:.1f} → {speed:.1f} (缩放: {self.speed_scale_factor * self.global_speed_multiplier:.3f})")

        # 速度死区
        if abs(speed) < 0.1:
            speed = 0
            print(f"  → 速度太小，设为0")

        if speed == 0:
            duty = self.horizontal_stop_duty
        elif speed > 0:
            duty_range = self.horizontal_cw_duty - self.horizontal_stop_duty
            duty_change = (abs(speed) / 100.0) * duty_range
            duty = self.horizontal_stop_duty + duty_change
            duty = min(duty, self.horizontal_cw_duty)
        else:
            duty_range = self.horizontal_stop_duty - self.horizontal_ccw_duty
            duty_change = (abs(speed) / 100.0) * duty_range
            duty = self.horizontal_stop_duty - duty_change
            duty = max(duty, self.horizontal_ccw_duty)

        try:
            self.horizontal_pwm.duty(duty)
            print(f"✓ PWM设置: 速度={speed:.1f}%, 占空比={duty:.3f}%")
        except Exception as e:
            print(f"❌ PWM设置失败: {e}")

    def pid_control(self, err_x, err_y):
        """PID控制（简化版，与main.py逻辑一致）"""
        print(f"\n🔍 PID控制: err_x={err_x}, err_y={err_y}")

        horizontal_error = -err_x  # 与main.py保持一致
        print(f"  horizontal_error = {horizontal_error}")

        # 逼近控制逻辑（与main.py一致）
        approach_threshold = 3
        fine_zone = 12
        slow_zone = 25
        normal_zone = 50

        if abs(horizontal_error) <= approach_threshold:
            horizontal_speed = 0
            self.horizontal_error_sum = 0
            control_zone = "逼近区域"
        elif abs(horizontal_error) <= fine_zone:
            horizontal_speed = horizontal_error * 0.3
            horizontal_speed = max(-4, min(4, horizontal_speed))
            control_zone = "精细区域"
        elif abs(horizontal_error) <= slow_zone:
            horizontal_speed = horizontal_error * 0.4
            horizontal_speed = max(-8, min(8, horizontal_speed))
            control_zone = "减速区域"
        elif abs(horizontal_error) <= normal_zone:
            horizontal_speed = horizontal_error * 0.5
            horizontal_speed = max(-12, min(12, horizontal_speed))
            control_zone = "正常区域"
        else:
            # PID控制
            self.horizontal_error_sum += horizontal_error
            self.horizontal_error_sum = max(-50, min(50, self.horizontal_error_sum))

            horizontal_derivative = horizontal_error - self.horizontal_last_error

            horizontal_output = (self.horizontal_pid_kp * horizontal_error +
                               self.horizontal_pid_ki * self.horizontal_error_sum +
                               self.horizontal_pid_kd * horizontal_derivative)

            horizontal_speed = max(-16, min(16, horizontal_output))
            control_zone = "PID区域"

        print(f"  → {control_zone}: 速度={horizontal_speed:.1f}%")

        # 设置舵机速度
        self.set_horizontal_speed(horizontal_speed)

        # 更新上次误差
        self.horizontal_last_error = horizontal_error

    def stop_horizontal(self):
        """停止水平舵机"""
        self.set_horizontal_speed(0)

def test_main_servo_controller():
    """测试main.py中的舵机控制逻辑"""
    print("🔧 测试main.py中的舵机控制逻辑")
    print("="*40)

    try:
        # 创建简化的舵机控制器
        servo_controller = SimpleServoController()
        print("✅ 舵机控制器初始化成功")

        # 测试参数
        print(f"全局速度倍数: {servo_controller.global_speed_multiplier}")
        print(f"水平PWM参数: 停止={servo_controller.horizontal_stop_duty}, 顺时针={servo_controller.horizontal_cw_duty}, 逆时针={servo_controller.horizontal_ccw_duty}")

        # 测试直接速度设置
        print("\n1. 测试直接速度设置:")
        test_speeds = [10, -10, 20, -20, 0]

        for speed in test_speeds:
            print(f"\n  设置速度: {speed}%")
            servo_controller.set_horizontal_speed(speed)
            time.sleep(2)

        # 测试PID控制
        print("\n2. 测试PID控制:")
        test_errors = [
            (30, 0, "大误差向右"),
            (-30, 0, "大误差向左"),
            (10, 0, "小误差向右"),
            (-10, 0, "小误差向左"),
            (0, 0, "无误差")
        ]

        for err_x, err_y, description in test_errors:
            print(f"\n  测试: {description} (err_x={err_x}, err_y={err_y})")
            servo_controller.pid_control(err_x, err_y)
            time.sleep(3)

            # 停止舵机
            print("  停止舵机...")
            servo_controller.stop_horizontal()
            time.sleep(1)

        print("\n3. 测试误差阈值逻辑:")
        # 模拟main.py中的误差阈值检查
        servo_error_threshold = 1  # 与main.py保持一致

        test_error_cases = [
            (2, 0, "误差=2px"),
            (1, 0, "误差=1px"),
            (0.5, 0, "误差=0.5px"),
            (5, 0, "误差=5px"),
            (10, 0, "误差=10px")
        ]

        for err_x, err_y, description in test_error_cases:
            error_magnitude = math.sqrt(err_x**2 + err_y**2)

            print(f"\n  {description}: 误差大小={error_magnitude:.1f}px")

            if error_magnitude > servo_error_threshold:
                print(f"    ✅ 启动PID控制 (>{servo_error_threshold}px)")
                servo_controller.pid_control(err_x, err_y)
            else:
                print(f"    ⏸ 停止舵机 (<={servo_error_threshold}px)")
                servo_controller.stop_horizontal()

            time.sleep(2)

        print("\n✅ 测试完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_speed_scaling():
    """测试速度缩放逻辑"""
    print("\n🔍 测试速度缩放逻辑")
    print("="*25)
    
    # 模拟main.py中的速度缩放
    GLOBAL_SPEED_MULTIPLIER = 0.4
    speed_scale_factor = 1.0
    
    test_speeds = [5, 10, 15, 20, -5, -10, -15, -20]
    
    for original_speed in test_speeds:
        # 应用缩放（与main.py中set_horizontal_speed一致）
        speed = max(-100, min(100, original_speed))
        speed = speed * speed_scale_factor * GLOBAL_SPEED_MULTIPLIER
        
        # 速度过滤
        if abs(speed) < 0.1:
            final_speed = 0
            filtered = "是"
        else:
            final_speed = speed
            filtered = "否"
        
        print(f"  原始速度: {original_speed:3.0f}% → 缩放后: {speed:5.2f}% → 最终: {final_speed:5.2f}% (被过滤: {filtered})")

def check_pwm_calculation():
    """检查PWM占空比计算"""
    print("\n📊 检查PWM占空比计算")
    print("="*25)
    
    # main.py中的PWM参数
    horizontal_stop_duty = 7.5
    horizontal_cw_duty = 8.5
    horizontal_ccw_duty = 6.5
    
    test_speeds = [1, 2, 5, 10, 20, -1, -2, -5, -10, -20]
    
    for speed in test_speeds:
        if speed == 0:
            duty = horizontal_stop_duty
        elif speed > 0:
            duty_range = horizontal_cw_duty - horizontal_stop_duty
            duty_change = (abs(speed) / 100.0) * duty_range
            duty = horizontal_stop_duty + duty_change
            duty = min(duty, horizontal_cw_duty)
        else:
            duty_range = horizontal_stop_duty - horizontal_ccw_duty
            duty_change = (abs(speed) / 100.0) * duty_range
            duty = horizontal_stop_duty - duty_change
            duty = max(duty, horizontal_ccw_duty)
        
        change = duty - horizontal_stop_duty
        print(f"  速度: {speed:3.0f}% → PWM: {duty:.3f}% (变化: {change:+.3f}%)")

def main():
    """主函数"""
    print("🔧 main.py水平舵机调试工具")
    print("="*35)
    
    try:
        test_speed_scaling()
        check_pwm_calculation()
        test_main_servo_controller()
        
        print("\n" + "="*35)
        print("🏁 调试完成！")
        print("\n📋 如果舵机还是不动，可能的原因：")
        print("1. 速度被过度缩放（检查GLOBAL_SPEED_MULTIPLIER）")
        print("2. 误差阈值设置不当（检查servo_error_threshold）")
        print("3. PID参数过小（检查PID系数）")
        print("4. PWM变化幅度不够（检查PWM范围）")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
